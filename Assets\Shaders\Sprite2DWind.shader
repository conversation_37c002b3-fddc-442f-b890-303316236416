Shader "Custom/Sprite2DWind"
{
    Properties
    {
        [MainTexture] _MainTex ("Sprite Texture", 2D) = "white" {}
        _Color ("Tint", Color) = (1,1,1,1)
        
        [Header(Wind Settings)]
        _WindNoiseTex ("Wind Noise Texture", 2D) = "white" {}
        _WindStrength ("Wind Strength", Range(0, 1)) = 0.3
        _WindSpeed ("Wind Speed", Range(0, 10)) = 2.0
        _WindDirection ("Wind Direction", Vector) = (1, 0, 0, 0)
        _WindNoiseScale ("Wind Noise Scale", Range(0.1, 5)) = 1.0
        _WindPulseFrequency ("Wind Pulse Frequency", Range(0, 5)) = 1.0
        _WindPulseAmplitude ("Wind Pulse Amplitude", Range(0, 1)) = 0.2
        
        [Header(Masking)]
        _BaseInfluence ("Base Influence (0=rigid base)", Range(0, 1)) = 0.1
        _TopInfluence ("Top Influence", Range(0, 2)) = 1.0
        
        [Header(Sprite Settings)]
        [MaterialToggle] PixelSnap ("Pixel snap", Float) = 0
        [HideInInspector] _RendererColor ("RendererColor", Color) = (1,1,1,1)
        [HideInInspector] _Flip ("Flip", Vector) = (1,1,1,1)
        [PerRendererData] _AlphaTex ("External Alpha", 2D) = "white" {}
        [PerRendererData] _EnableExternalAlpha ("Enable External Alpha", Float) = 0
    }

    SubShader
    {
        Tags
        {
            "Queue"="Transparent"
            "IgnoreProjector"="True"
            "RenderType"="Transparent"
            "PreviewType"="Plane"
            "CanUseSpriteAtlas"="True"
            "RenderPipeline" = "UniversalPipeline"
        }

        Cull Off
        Lighting Off
        ZWrite Off
        Blend One OneMinusSrcAlpha

        Pass
        {
            Tags { "LightMode" = "Universal2D" }
            
            HLSLPROGRAM
            #pragma vertex WindVert
            #pragma fragment SpriteFrag
            #pragma target 2.0
            #pragma multi_compile_instancing
            #pragma multi_compile_local _ PIXELSNAP_ON
            #pragma multi_compile _ ETC1_EXTERNAL_ALPHA
            
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            
            struct appdata_t
            {
                float4 vertex   : POSITION;
                float4 color    : COLOR;
                float2 texcoord : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f
            {
                float4 vertex   : SV_POSITION;
                float4 color    : COLOR;
                float2 texcoord : TEXCOORD0;
                UNITY_VERTEX_OUTPUT_STEREO
            };
            
            CBUFFER_START(UnityPerMaterial)
                float4 _MainTex_ST;
                float4 _WindNoiseTex_ST;
                half4 _Color;
                half4 _RendererColor;
                float4 _Flip;
                float _WindStrength;
                float _WindSpeed;
                float4 _WindDirection;
                float _WindNoiseScale;
                float _WindPulseFrequency;
                float _WindPulseAmplitude;
                float _BaseInfluence;
                float _TopInfluence;
                float _EnableExternalAlpha;
                // Pre-calculated values for optimization
                float4 _WindTimeData; // x: time*speed, y: sin(pulse), z: cos(pulse), w: pulse_amplitude
                float2 _WindDirectionNorm; // Pre-normalized wind direction
            CBUFFER_END

            TEXTURE2D(_MainTex);
            SAMPLER(sampler_MainTex);
            TEXTURE2D(_WindNoiseTex);
            SAMPLER(sampler_WindNoiseTex);
            TEXTURE2D(_AlphaTex);
            SAMPLER(sampler_AlphaTex);


            // Optimized wind calculation function
            float2 CalculateWindOffset(float2 worldPos, float heightFactor)
            {
                // Use pre-calculated time and pulse values
                float time = _WindTimeData.x;
                float pulseWave = _WindTimeData.y * _WindPulseAmplitude + (1.0 - _WindPulseAmplitude);

                // Simplified single noise sample with offset for variation
                float2 noiseUV = worldPos * _WindNoiseScale * 0.1 + float2(time * 0.05, time * 0.02);
                float4 noiseSample = SAMPLE_TEXTURE2D_LOD(_WindNoiseTex, sampler_WindNoiseTex, noiseUV, 0);

                // Combine both noise channels for variation
                float combinedNoise = noiseSample.r + (noiseSample.g - 0.5) * 0.3;

                // Use pre-normalized direction
                return _WindDirectionNorm * _WindStrength * heightFactor * combinedNoise * pulseWave;
            }

            v2f WindVert(appdata_t IN)
            {
                v2f OUT;
                UNITY_SETUP_INSTANCE_ID(IN);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(OUT);

                // Use object space position for 2D sprites (more efficient than world space)
                float2 objectPos = IN.vertex.xy;

                float heightFactor = saturate(IN.texcoord.y);
                heightFactor = lerp(_BaseInfluence, _TopInfluence, heightFactor);

                // Calculate wind offset using optimized function
                float2 windOffset = CalculateWindOffset(objectPos, heightFactor);
                IN.vertex.xy += windOffset;

                #ifdef UNITY_INSTANCING_ENABLED
                    IN.vertex.xy *= _Flip.xy;
                #endif

                OUT.vertex = TransformObjectToHClip(IN.vertex.xyz);

                #ifdef PIXELSNAP_ON
                    // Manual pixel snapping for URP
                    float4 pixelPos = OUT.vertex;
                    pixelPos.xyz = pixelPos.xyz / pixelPos.w;
                    pixelPos.xy = floor(pixelPos.xy * _ScreenParams.xy + 0.5) / _ScreenParams.xy;
                    pixelPos.xyz *= pixelPos.w;
                    OUT.vertex = pixelPos;
                #endif

                OUT.texcoord = TRANSFORM_TEX(IN.texcoord, _MainTex);
                OUT.color = IN.color * _Color * _RendererColor;

                return OUT;
            }

            half4 SpriteFrag(v2f IN) : SV_Target
            {
                half4 texColor = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, IN.texcoord);

                #if ETC1_EXTERNAL_ALPHA
                    half4 alpha = SAMPLE_TEXTURE2D(_AlphaTex, sampler_AlphaTex, IN.texcoord);
                    texColor.a = lerp(texColor.a, alpha.r, _EnableExternalAlpha);
                #endif

                texColor *= IN.color;
                texColor.rgb *= texColor.a;

                return texColor;
            }
            ENDHLSL
        }
        
        Pass
        {
            Tags { "LightMode" = "UniversalForward" }
            
            HLSLPROGRAM
            #pragma vertex WindVert
            #pragma fragment SpriteFrag
            #pragma target 2.0
            #pragma multi_compile_instancing
            #pragma multi_compile_local _ PIXELSNAP_ON
            #pragma multi_compile _ ETC1_EXTERNAL_ALPHA
            
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            
            struct appdata_t
            {
                float4 vertex   : POSITION;
                float4 color    : COLOR;
                float2 texcoord : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f
            {
                float4 vertex   : SV_POSITION;
                float4 color    : COLOR;
                float2 texcoord : TEXCOORD0;
                UNITY_VERTEX_OUTPUT_STEREO
            };
            
            CBUFFER_START(UnityPerMaterial)
                float4 _MainTex_ST;
                float4 _WindNoiseTex_ST;
                half4 _Color;
                half4 _RendererColor;
                float4 _Flip;
                float _WindStrength;
                float _WindSpeed;
                float4 _WindDirection;
                float _WindNoiseScale;
                float _WindPulseFrequency;
                float _WindPulseAmplitude;
                float _BaseInfluence;
                float _TopInfluence;
                float _EnableExternalAlpha;
                // Pre-calculated values for optimization
                float4 _WindTimeData; // x: time*speed, y: sin(pulse), z: cos(pulse), w: pulse_amplitude
                float2 _WindDirectionNorm; // Pre-normalized wind direction
            CBUFFER_END

            TEXTURE2D(_MainTex);
            SAMPLER(sampler_MainTex);
            TEXTURE2D(_WindNoiseTex);
            SAMPLER(sampler_WindNoiseTex);
            TEXTURE2D(_AlphaTex);
            SAMPLER(sampler_AlphaTex);


            // Optimized wind calculation function
            float2 CalculateWindOffset(float2 worldPos, float heightFactor)
            {
                // Use pre-calculated time and pulse values
                float time = _WindTimeData.x;
                float pulseWave = _WindTimeData.y * _WindPulseAmplitude + (1.0 - _WindPulseAmplitude);

                // Simplified single noise sample with offset for variation
                float2 noiseUV = worldPos * _WindNoiseScale * 0.1 + float2(time * 0.05, time * 0.02);
                float4 noiseSample = SAMPLE_TEXTURE2D_LOD(_WindNoiseTex, sampler_WindNoiseTex, noiseUV, 0);

                // Combine both noise channels for variation
                float combinedNoise = noiseSample.r + (noiseSample.g - 0.5) * 0.3;

                // Use pre-normalized direction
                return _WindDirectionNorm * _WindStrength * heightFactor * combinedNoise * pulseWave;
            }

            v2f WindVert(appdata_t IN)
            {
                v2f OUT;
                UNITY_SETUP_INSTANCE_ID(IN);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(OUT);

                // Use object space position for 2D sprites (more efficient than world space)
                float2 objectPos = IN.vertex.xy;

                float heightFactor = saturate(IN.texcoord.y);
                heightFactor = lerp(_BaseInfluence, _TopInfluence, heightFactor);

                // Calculate wind offset using optimized function
                float2 windOffset = CalculateWindOffset(objectPos, heightFactor);
                IN.vertex.xy += windOffset;

                #ifdef UNITY_INSTANCING_ENABLED
                    IN.vertex.xy *= _Flip.xy;
                #endif

                OUT.vertex = TransformObjectToHClip(IN.vertex.xyz);

                #ifdef PIXELSNAP_ON
                    // Manual pixel snapping for URP
                    float4 pixelPos = OUT.vertex;
                    pixelPos.xyz = pixelPos.xyz / pixelPos.w;
                    pixelPos.xy = floor(pixelPos.xy * _ScreenParams.xy + 0.5) / _ScreenParams.xy;
                    pixelPos.xyz *= pixelPos.w;
                    OUT.vertex = pixelPos;
                #endif

                OUT.texcoord = TRANSFORM_TEX(IN.texcoord, _MainTex);
                OUT.color = IN.color * _Color * _RendererColor;

                return OUT;
            }

            half4 SpriteFrag(v2f IN) : SV_Target
            {
                half4 texColor = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, IN.texcoord);

                #if ETC1_EXTERNAL_ALPHA
                    half4 alpha = SAMPLE_TEXTURE2D(_AlphaTex, sampler_AlphaTex, IN.texcoord);
                    texColor.a = lerp(texColor.a, alpha.r, _EnableExternalAlpha);
                #endif

                texColor *= IN.color;
                texColor.rgb *= texColor.a;

                return texColor;
            }
            ENDHLSL
        }
    }
    
    Fallback "Sprites/Default"
}